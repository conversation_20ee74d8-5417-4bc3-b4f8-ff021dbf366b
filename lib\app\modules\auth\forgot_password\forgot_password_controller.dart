import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../core/services/network_service.dart';

class ForgotPasswordController extends GetxController {
  final formKey = GlobalKey<FormState>();
  final emailController = TextEditingController();
  
  final RxBool isLoading = false.obs;
  final RxBool emailSent = false.obs;

  @override
  void onClose() {
    emailController.dispose();
    super.onClose();
  }

  String? validateEmail(String? value) {
    if (value == null || value.isEmpty) {
      return 'Email is required';
    }
    if (!GetUtils.isEmail(value)) {
      return 'Please enter a valid email';
    }
    return null;
  }

  Future<void> sendResetEmail() async {
    if (!formKey.currentState!.validate()) return;

    try {
      isLoading.value = true;
      
      final response = await NetworkService.to.post('/auth/forgot-password', data: {
        'email': emailController.text.trim(),
      });

      if (response.statusCode == 200) {
        emailSent.value = true;
        Get.snackbar(
          'Success',
          'Password reset email sent successfully',
          snackPosition: SnackPosition.TOP,
          backgroundColor: Colors.green.withOpacity(0.8),
          colorText: Colors.white,
        );
      }
    } catch (e) {
      Get.snackbar(
        'Error',
        'Failed to send reset email. Please try again.',
        snackPosition: SnackPosition.TOP,
        backgroundColor: Colors.red.withOpacity(0.8),
        colorText: Colors.white,
      );
    } finally {
      isLoading.value = false;
    }
  }

  void goBack() {
    Get.back();
  }

  void resendEmail() {
    emailSent.value = false;
    sendResetEmail();
  }
}
