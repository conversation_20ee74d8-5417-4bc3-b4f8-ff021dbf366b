import 'package:flutter/material.dart';
import 'package:flex_color_scheme/flex_color_scheme.dart';
import 'package:google_fonts/google_fonts.dart';
import 'app_colors.dart';

class AppTheme {
  static ThemeData get lightTheme {
    return FlexThemeData.light(
      scheme: FlexScheme.custom,
      primary: AppColors.primaryPastel,
      secondary: AppColors.secondaryPastel,
      tertiary: AppColors.accentPastel,
      surface: AppColors.backgroundPastel,
      background: AppColors.backgroundPastel,
      scaffoldBackground: AppColors.backgroundPastel,
      appBarStyle: FlexAppBarStyle.background,
      subThemesData: const FlexSubThemesData(
        blendOnLevel: 10,
        blendOnColors: false,
        useTextTheme: true,
        useM2StyleDividerInM3: true,
        elevatedButtonSchemeColor: SchemeColor.onPrimaryContainer,
        elevatedButtonSecondarySchemeColor: SchemeColor.primaryContainer,
        outlinedButtonOutlineSchemeColor: SchemeColor.primary,
        toggleButtonsBorderSchemeColor: SchemeColor.primary,
        segmentedButtonSchemeColor: SchemeColor.primary,
        segmentedButtonBorderSchemeColor: SchemeColor.primary,
        unselectedToggleIsColored: true,
        sliderBaseSchemeColor: SchemeColor.primary,
        sliderIndicatorSchemeColor: SchemeColor.onPrimary,
        sliderValueTinted: true,
        inputDecoratorSchemeColor: SchemeColor.primary,
        inputDecoratorIsFilled: true,
        inputDecoratorBorderType: FlexInputBorderType.outline,
        inputDecoratorRadius: 16.0,
        inputDecoratorUnfocusedHasBorder: true,
        inputDecoratorFocusedBorderWidth: 2.0,
        fabUseShape: true,
        fabAlwaysCircular: true,
        fabSchemeColor: SchemeColor.tertiary,
        chipSchemeColor: SchemeColor.primaryContainer,
        chipSelectedSchemeColor: SchemeColor.primary,
        chipRadius: 16.0,
        cardRadius: 16.0,
        cardElevation: 8.0,
        popupMenuRadius: 16.0,
        popupMenuElevation: 8.0,
        dialogRadius: 20.0,
        dialogElevation: 16.0,
        timePickerDialogRadius: 20.0,
        snackBarRadius: 16.0,
        snackBarElevation: 8.0,
        appBarScrolledUnderElevation: 4.0,
        bottomSheetRadius: 20.0,
        bottomSheetElevation: 16.0,
        bottomNavigationBarMutedUnselectedLabel: true,
        bottomNavigationBarMutedUnselectedIcon: true,
        menuRadius: 16.0,
        menuElevation: 8.0,
        menuBarRadius: 16.0,
        menuBarElevation: 4.0,
        navigationBarSelectedLabelSchemeColor: SchemeColor.onSurface,
        navigationBarMutedUnselectedLabel: true,
        navigationBarMutedUnselectedIcon: true,
        navigationBarIndicatorSchemeColor: SchemeColor.secondaryContainer,
        navigationBarIndicatorOpacity: 0.24,
        navigationBarIndicatorRadius: 16.0,
        navigationRailSelectedLabelSchemeColor: SchemeColor.onSurface,
        navigationRailMutedUnselectedLabel: true,
        navigationRailMutedUnselectedIcon: true,
        navigationRailIndicatorSchemeColor: SchemeColor.secondaryContainer,
        navigationRailIndicatorOpacity: 0.24,
        navigationRailIndicatorRadius: 16.0,
      ),
      keyColors: const FlexKeyColors(
        useSecondary: true,
        useTertiary: true,
        keepPrimary: true,
        keepSecondary: true,
        keepTertiary: true,
      ),
      visualDensity: FlexColorScheme.comfortablePlatformDensity,
      useMaterial3: true,
      swapLegacyOnMaterial3: true,
      fontFamily: GoogleFonts.inter().fontFamily,
    );
  }

  static ThemeData get darkTheme {
    return FlexThemeData.dark(
      scheme: FlexScheme.custom,
      primary: AppColors.primaryPastel,
      secondary: AppColors.secondaryPastel,
      tertiary: AppColors.accentPastel,
      surface: const Color(0xFF1A1A2E),
      background: const Color(0xFF16213E),
      scaffoldBackground: const Color(0xFF0F172A),
      appBarStyle: FlexAppBarStyle.background,
      subThemesData: const FlexSubThemesData(
        blendOnLevel: 20,
        blendOnColors: false,
        useTextTheme: true,
        useM2StyleDividerInM3: true,
        elevatedButtonSchemeColor: SchemeColor.onPrimaryContainer,
        elevatedButtonSecondarySchemeColor: SchemeColor.primaryContainer,
        outlinedButtonOutlineSchemeColor: SchemeColor.primary,
        toggleButtonsBorderSchemeColor: SchemeColor.primary,
        segmentedButtonSchemeColor: SchemeColor.primary,
        segmentedButtonBorderSchemeColor: SchemeColor.primary,
        unselectedToggleIsColored: true,
        sliderBaseSchemeColor: SchemeColor.primary,
        sliderIndicatorSchemeColor: SchemeColor.onPrimary,
        sliderValueTinted: true,
        inputDecoratorSchemeColor: SchemeColor.primary,
        inputDecoratorIsFilled: true,
        inputDecoratorBorderType: FlexInputBorderType.outline,
        inputDecoratorRadius: 16.0,
        inputDecoratorUnfocusedHasBorder: true,
        inputDecoratorFocusedBorderWidth: 2.0,
        fabUseShape: true,
        fabAlwaysCircular: true,
        fabSchemeColor: SchemeColor.tertiary,
        chipSchemeColor: SchemeColor.primaryContainer,
        chipSelectedSchemeColor: SchemeColor.primary,
        chipRadius: 16.0,
        cardRadius: 16.0,
        cardElevation: 8.0,
        popupMenuRadius: 16.0,
        popupMenuElevation: 8.0,
        dialogRadius: 20.0,
        dialogElevation: 16.0,
        timePickerDialogRadius: 20.0,
        snackBarRadius: 16.0,
        snackBarElevation: 8.0,
        appBarScrolledUnderElevation: 4.0,
        bottomSheetRadius: 20.0,
        bottomSheetElevation: 16.0,
        bottomNavigationBarMutedUnselectedLabel: true,
        bottomNavigationBarMutedUnselectedIcon: true,
        menuRadius: 16.0,
        menuElevation: 8.0,
        menuBarRadius: 16.0,
        menuBarElevation: 4.0,
        navigationBarSelectedLabelSchemeColor: SchemeColor.onSurface,
        navigationBarMutedUnselectedLabel: true,
        navigationBarMutedUnselectedIcon: true,
        navigationBarIndicatorSchemeColor: SchemeColor.secondaryContainer,
        navigationBarIndicatorOpacity: 0.24,
        navigationBarIndicatorRadius: 16.0,
        navigationRailSelectedLabelSchemeColor: SchemeColor.onSurface,
        navigationRailMutedUnselectedLabel: true,
        navigationRailMutedUnselectedIcon: true,
        navigationRailIndicatorSchemeColor: SchemeColor.secondaryContainer,
        navigationRailIndicatorOpacity: 0.24,
        navigationRailIndicatorRadius: 16.0,
      ),
      keyColors: const FlexKeyColors(
        useSecondary: true,
        useTertiary: true,
        keepPrimary: true,
        keepSecondary: true,
        keepTertiary: true,
      ),
      visualDensity: FlexColorScheme.comfortablePlatformDensity,
      useMaterial3: true,
      swapLegacyOnMaterial3: true,
      fontFamily: GoogleFonts.inter().fontFamily,
    );
  }
}
