import 'package:get/get.dart';
import '../../core/services/storage_service.dart';
import '../../routes/app_routes.dart';
import '../../core/constants/app_constants.dart';

class SplashController extends GetxController with GetSingleTickerProviderStateMixin {
  final RxBool isLoading = true.obs;
  final RxDouble logoScale = 0.0.obs;
  final RxDouble logoOpacity = 0.0.obs;
  final RxDouble textOpacity = 0.0.obs;

  @override
  void onInit() {
    super.onInit();
    _startAnimation();
  }

  void _startAnimation() async {
    // Logo scale animation
    await Future.delayed(const Duration(milliseconds: 500));
    logoScale.value = 1.0;
    logoOpacity.value = 1.0;

    // Text fade in
    await Future.delayed(const Duration(milliseconds: 800));
    textOpacity.value = 1.0;

    // Wait for minimum splash duration
    await Future.delayed(AppConstants.splashDuration);
    
    _navigateToNextScreen();
  }

  void _navigateToNextScreen() {
    final storageService = StorageService.to;
    
    if (storageService.isFirstTime) {
      // First time user - go to intro
      Get.offAllNamed(AppRoutes.intro);
    } else if (storageService.isLoggedIn) {
      // Logged in user - go to home
      Get.offAllNamed(AppRoutes.home);
    } else {
      // Not logged in - go to login
      Get.offAllNamed(AppRoutes.login);
    }
  }
}
